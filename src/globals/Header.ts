import type { GlobalConfig } from "payload";
import { link } from "@/fields/link";
import { revalidatePath, revalidateTag } from "next/cache";

export const Header: GlobalConfig = {
	slug: "header",
	label: "Navigation",
	access: {
		read: () => true,
	},
	fields: [
		{
			name: "navItems",
			label: "Navigationselemente",
			type: "array",
			maxRows: 6,
			admin: {
				initCollapsed: true,
				components: {
					RowLabel: "@/globals/RowLabel#RowLabel",
				},
			},
			fields: [
				{
					type: "row",
					fields: [
						{
							name: "isMegaNav",
							label: "Gibts Unterseiten?",
							type: "checkbox",
							defaultValue: false,
						},
					],
				},
				link({
					appearances: false,
					overrides: {
						admin: {
							condition: (_, siblingData) => siblingData.isMegaNav !== true,
						},
					},
				}),
				{
					name: "label",
					type: "text",
					label: "Label",
					admin: {
						condition: (_, siblingData) => siblingData.isMegaNav === true,
					},
				},
				{
					name: "children",
					label: "Unterseiten",
					type: "array",
					admin: {
						condition: (_, siblingData) => siblingData.isMegaNav === true,
					},
					fields: [link({ appearances: false })],
				},
			],
		},
	],
	hooks: {
		afterChange: [
			async ({ req: { payload, context } }) => {
				// Skip revalidation during admin operations to avoid render cycle issues
				// Only revalidate when explicitly enabled via context
				if (!context.disableRevalidate && context.enableRevalidation) {
					const pages = await payload.find({
						collection: "pages",
						depth: 1,
						limit: 0,
					});

					for (const page of pages.docs) {
						const path = page.slug === "home" ? "/" : `/${page.slug}`;

						payload.logger.info(`Revalidating page at path: ${path}`);

						try {
							revalidatePath(path);
							revalidateTag("pages-sitemap");
						} catch (error) {
							payload.logger.error(`Failed to revalidate path ${path}:`, error);
						}
					}
				} else if (!context.disableRevalidate) {
					payload.logger.info(
						"Skipping header revalidation - admin context detected",
					);
				}
			},
		],
	},
};
