import { lexicalEditor } from "@payloadcms/richtext-lexical";
import { format, parseISO } from "date-fns";
import { de } from "date-fns/locale";
import type { CollectionConfig } from "payload";
import { revalidateAllPages } from "./hooks/revalidate-all-pages";

const formatConcertDate = (
	dates: { date: string; startTime: string }[],
): string => {
	if (!dates.length) return "";

	const sorted = dates
		.map((d) => ({ ...d, dateObj: parseISO(d.date) }))
		.sort((a, b) => a.dateObj.getTime() - b.dateObj.getTime());

	const groups: Record<string, typeof sorted> = {};
	for (const d of sorted) {
		const key = format(d.dateObj, "MMMM yyyy", { locale: de });
		if (!groups[key]) groups[key] = [];
		groups[key].push(d);
	}

	const result = Object.entries(groups).map(([monthYear, days]) => {
		const datesFormatted = days
			.map((d) => {
				// Get abbreviated weekday, remove trailing period (e.g., "Sa.")
				const weekday = format(d.dateObj, "EEE", { locale: de }).replace(
					/\.$/,
					"",
				);
				const day = format(d.dateObj, "d.", { locale: de });
				return `${weekday}, ${day}`;
			})
			.join(" & ");
		const time = days[0].startTime;
		return `${datesFormatted} ${monthYear} | ${time}`;
	});

	return result.join(", ");
};

export const Concerts: CollectionConfig = {
	slug: "concerts",
	labels: {
		singular: "Konzert",
		plural: "Konzerte",
	},
	defaultSort: "-dates.date",
	admin: {
		useAsTitle: "title",
		defaultColumns: ["title", "where", "formattedDateString"],
	},
	fields: [
		{
			type: "row",
			fields: [
				{
					name: "title",
					label: "Titel",
					type: "text",
					admin: { width: "50%" },
					required: true,
				},
				{
					name: "subline",
					label: "Untertitel",
					type: "text",
					admin: { width: "50%" },
				},
			],
		},
		{
			type: "row",
			fields: [
				{
					name: "image",
					relationTo: "media",
					label: "Bild",
					type: "upload",
					admin: { width: "50%" },
				},
				{
					name: "where",
					label: "Wo?",
					type: "text",
					required: true,
					admin: { width: "50%" },
				},
			],
		},
		{
			type: "row",
			fields: [
				{
					name: "who",
					label: "Wer?",
					type: "text",
					required: true,
					admin: { width: "50%" },
				},
				{
					name: "lead",
					label: "Leitung",
					type: "text",
					required: true,
					admin: { width: "50%" },
				},
			],
		},
		{
			name: "dates",
			label: "Daten & Uhrzeiten",
			type: "array",
			required: true,
			fields: [
				{
					name: "date",
					label: "Datum",
					type: "date",
					required: true,
				},
				{
					name: "startTime",
					label: "Uhrzeit",
					type: "text",
					required: true,
				},
			],
		},
		{
			name: "formattedDateString",
			type: "text",
			label: "Wann?",
			admin: {
				hidden: true,
			},
		},
		{
			name: "cancelled",
			label: "Abgesagt?",
			type: "checkbox",
			defaultValue: false,
			admin: {
				position: "sidebar",
			},
		},
		{
			name: "status",
			type: "select",
			label: "Status",
			admin: {
				position: "sidebar",
				readOnly: true,
			},
			options: [
				{ label: "Zukünftig", value: "upcoming" },
				{ label: "Vorbei", value: "past" },
			],
		},
		{
			name: "program",
			type: "richText",
			label: "Programm",
			editor: lexicalEditor({
				features: ({ rootFeatures }) => rootFeatures,
			}),
		},
	],
	hooks: {
		afterChange: [revalidateAllPages],
		beforeChange: [
			async ({ data }) => {
				if (!data.dates || !Array.isArray(data.dates)) return data;

				const formatted = formatConcertDate(data.dates);
				return {
					...data,
					formattedDateString: formatted,
				};
			},
			async ({ data }) => {
				if (!data.dates || !Array.isArray(data.dates)) return data;

				const now = new Date();
				const upcoming = data.dates.some((d) => {
					const date = parseISO(d.date);
					return date > now;
				});

				return {
					...data,
					status: upcoming ? "upcoming" : "past",
				};
			},
		],
	},
	versions: {
		drafts: {
			autosave: {
				interval: 100,
			},
			schedulePublish: true,
		},
		maxPerDoc: 50,
	},
};
