import type {
	CollectionAfter<PERSON>hangeHook,
	CollectionAfterDeleteHook,
	CollectionBeforeChangeHook,
} from "payload";

import { revalidatePath, revalidateTag } from "next/cache";

import type { Page } from "@/payload-types";

export const revalidatePage: CollectionAfterChangeHook<Page> = ({
	doc,
	previousDoc,
	req: { payload, context },
}) => {
	if (!context.disableRevalidate) {
		// Use setTimeout to defer revalidation outside of the render cycle
		setTimeout(() => {
			if (doc._status === "published") {
				const path = doc.slug === "home" ? "/" : `/${doc.slug}`;

				payload.logger.info(`Revalidating page at path: ${path}`);

				try {
					revalidatePath(path);
					revalidateTag("pages-sitemap");
				} catch (error) {
					payload.logger.error(`Failed to revalidate path ${path}:`, error);
				}
			}

			// If the page was previously published, we need to revalidate the old path
			if (previousDoc?._status === "published" && doc._status !== "published") {
				const oldPath =
					previousDoc.slug === "home" ? "/" : `/${previousDoc.slug}`;

				payload.logger.info(`Revalidating old page at path: ${oldPath}`);

				try {
					revalidatePath(oldPath);
					revalidateTag("pages-sitemap");
				} catch (error) {
					payload.logger.error(`Failed to revalidate old path ${oldPath}:`, error);
				}
			}
		}, 0);
	}
	return doc;
};

export const revalidateDelete: CollectionAfterDeleteHook<Page> = ({
	doc,
	req: { context },
}) => {
	if (!context.disableRevalidate) {
		const path = doc?.slug === "home" ? "/" : `/${doc?.slug}`;
		revalidatePath(path);
		revalidateTag("pages-sitemap");
	}

	return doc;
};

export const populatePublishedAt: CollectionBeforeChangeHook = ({
	data,
	operation,
	req,
}) => {
	if (operation === "create" || operation === "update") {
		if (req.data && !req.data.publishedAt) {
			const now = new Date();
			return {
				...data,
				publishedAt: now,
			};
		}
	}

	return data;
};
