import type { Composer, Concert, Critic, Program } from "@/payload-types";
import { revalidatePath, revalidateTag } from "next/cache";
import type { CollectionAfterChangeHook } from "payload";

export const revalidateAllPages: CollectionAfter<PERSON>hangeHook<
	Composer | Concert | Critic | Program
> = async ({ doc, collection, req: { payload, context } }) => {
	if (!context.disableRevalidate) {
		const pages = await payload.find({
			collection: "pages",
			depth: 1,
			limit: 0,
		});

		// Use setTimeout to defer revalidation outside of the render cycle
		setTimeout(() => {
			for (const page of pages.docs) {
				const path = page.slug === "home" ? "/" : `/${page.slug}`;

				payload.logger.info(`Revalidating page at path: ${path}`);

				try {
					revalidatePath(path);
					revalidateTag("pages-sitemap");
				} catch (error) {
					payload.logger.error(`Failed to revalidate path ${path}:`, error);
				}
			}
		}, 0);
	}
	return doc;
};
