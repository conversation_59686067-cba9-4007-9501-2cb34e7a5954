import type { Composer, Concert, Critic, Program } from "@/payload-types";
import { revalidatePath, revalidateTag } from "next/cache";
import type { Block, CollectionAfterChangeHook, CollectionSlug } from "payload";

const COLLECTION_SLUG_BLOCK_TYPE_MAP: Record<
	Extract<CollectionSlug, "composers" | "concerts" | "critics" | "programs">,
	Block["slug"]
> = {
	composers: "composer-archive",
	concerts: "concert-archive",
	critics: "critic-archive",
	programs: "program-archive",
};

export const revalidateArchive: CollectionAfterChangeHook<
	Composer | Concert | Critic | Program
> = async ({ doc, collection, req: { payload, context } }) => {
	if (
		!context.disableRevalidate &&
		collection.slug in COLLECTION_SLUG_BLOCK_TYPE_MAP
	) {
		const blockType =
			COLLECTION_SLUG_BLOCK_TYPE_MAP[
				collection.slug as keyof typeof COLLECTION_SLUG_BLOCK_TYPE_MAP
			];

		const pages = await payload.find({
			collection: "pages",
			depth: 1,
			limit: 0,
			where: {
				"layout.blockType": {
					equals: blockType,
				},
			},
		});

		// Use setTimeout to defer revalidation outside of the render cycle
		setTimeout(() => {
			for (const page of pages.docs) {
				const path = page.slug === "home" ? "/" : `/${page.slug}`;

				payload.logger.info(`Revalidating page at path: ${path}`);

				try {
					revalidatePath(path);
					revalidateTag("pages-sitemap");
				} catch (error) {
					payload.logger.error(`Failed to revalidate path ${path}:`, error);
				}
			}
		}, 0);
	}
	return doc;
};
