import { useTransitionState } from "@/lib/transitions";
import { useGSAP } from "@gsap/react";
import gsap from "gsap";

export const useRevealer = () => {
	const { endTransition, isTransitioning } = useTransitionState();

	useGSAP(
		() => {
			if (!isTransitioning) return;

			const tl = gsap.timeline();

			tl.to(".revealer", {
				y: "-100%",
				duration: 2,
				delay: 1,
				ease: "expo.inOut",
			}).to(
				".revealer .punk-head",
				{
					rotate: "92deg",
					scale: 0.1,
					opacity: -2,
					y: "50vh",
					duration: 2,
					ease: "expo.inOut",
				},
				"<",
			);

			tl.call(() => endTransition(), undefined, "<33.33%");
		},
		{ dependencies: [isTransitioning] },
	);
};
