"use client";

import { cn } from "@/lib/utils";
import type { Concert } from "@/payload-types";
import type { FC } from "react";
import { Magnet } from "./motion/magnet";
import { Media } from "./render/render-media";
import Link from "next/link";
import { useIsMobile } from "@/hooks/use-is-mobile";

interface IConcertItemProps {
	concert: Concert;
	isMostRecent?: boolean;
	className?: string;
}

export const ConcertItem: FC<IConcertItemProps> = ({
	className,
	concert,
	isMostRecent,
}) => {
	const isMobile = useIsMobile();

	return (
		<div className={cn("", className)}>
			<div className="relative">
				{concert.image && (
					<Media
						resource={concert.image}
						className="aspect-video mb-8"
						imgClassName="object-cover h-full w-full"
					/>
				)}
				{isMostRecent && (
					<div className="absolute left-0 top-3/4 -translate-x-1/4 -translate-y-1/2">
						<Magnet padding={5000} disabled={isMobile}>
							<Link
								href={"/konzert/next"}
								scroll={false}
								className="size-40 bg-contrast rounded-full grid place-items-center text-base text-center leading-none uppercase font-bold cursor-pointer hover:scale-105 hover:bg-primary hover:text-contrast transition-all ease-out-expo duration-500"
							>
								Gib mir <br />
								mehr infos
							</Link>
						</Magnet>
					</div>
				)}
			</div>
			<div className="mb-8">
				<h3 className="text-balance">
					<strong>{concert.title}</strong>
					{concert.cancelled && (
						<>
							{" "}
							<span className="col-span-2 text-primary bg-contrast text-xs px-2 font-bold uppercase w-fit">
								ABGESAGT
							</span>
						</>
					)}
				</h3>
				<p className="h3 text-balance">{concert.subline}</p>
			</div>
			<div className="grid lg:[grid-template-columns:max-content_1fr] lg:gap-x-4 lg:gap-y-2">
				<p>
					<strong>WANN:</strong>
				</p>
				<p className="max-lg:mb-4">{concert.formattedDateString}</p>
				<p>
					<strong>WO:</strong>
				</p>
				<p>{concert.where}</p>
			</div>
			{!isMostRecent && (
				<Link
					href={`/konzert/${concert.id}`}
					scroll={false}
					className="variant--default cursor-pointer mt-8"
				>
					Mehr infos
				</Link>
			)}
		</div>
	);
};
