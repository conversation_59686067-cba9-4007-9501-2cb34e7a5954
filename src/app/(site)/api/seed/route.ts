import { getPayload } from "payload";
import configPromise from "@payload-config";
import { NextResponse } from "next/server";
import { programs } from "./programs";

export const GET = async () => {
	// Unauthorized
	return NextResponse.json({ error: "UNAUTHORIZED" }, { status: 401 });

	// try {
	// 	const payloadInstance = await getPayload({ config: configPromise });
	// 	const concerts = await payloadInstance.find({
	// 		collection: "concerts",
	// 		depth: 1,
	// 		// where: {
	// 		// 	_status: {
	// 		// 		not_equals: "published",
	// 		// 	},
	// 		// },
	// 		limit: 0,
	// 	});

	// 	await Promise.all(
	// 		concerts.docs.map(async (concert) => {
	// 			await payloadInstance.update({
	// 				collection: "concerts",
	// 				id: concert.id,
	// 				data: {
	// 					_status: "published",
	// 				},
	// 			});
	// 		}),
	// 	);

	// 	return NextResponse.json({ success: true });
	// } catch (error) {
	// 	console.error(error, error.data);
	// 	return NextResponse.json(
	// 		{ error: "INTERNAL_SERVER_ERROR" },
	// 		{ status: 500 },
	// 	);
	// }
};
