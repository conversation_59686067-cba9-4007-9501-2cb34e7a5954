import { ImageResponse } from "next/og";
import { readFile } from "node:fs/promises";
import { join } from "node:path";
import configPromise from "@payload-config";
import { getPayload } from "payload";
import type { Concert, Media } from "@/payload-types";

export const runtime = "nodejs";
export const alt = "Hard Chor Konzert";
export const size = { width: 1200, height: 630 };
export const contentType = "image/png";

interface IConcertDetailPageProps {
	params: { id: string };
}

export default async function Image({ params }: IConcertDetailPageProps) {
	const SwitzerRegular = await readFile(
		join(process.cwd(), "public/fonts/switzer/Switzer-Regular.ttf"),
	);

	const { id } = params;

	const payload = await getPayload({ config: configPromise });

	let concert: Concert | null = null;

	if (id === "next") {
		const result = await payload.find({
			collection: "concerts",
			depth: 1,
			where: {
				_status: { equals: "published" },
				"dates.date": { greater_than_equal: new Date().toISOString() },
			},
			sort: "dates.date",
			limit: 1,
		});
		concert = result.docs[0] ?? null;
	} else {
		concert = await payload.findByID({
			collection: "concerts",
			id,
			depth: 1,
		});
	}

	const imgUrl =
		typeof concert?.image === "object" && concert?.image?.url
			? new URL(
					concert.image.url as string,
					process.env.NEXT_PUBLIC_SERVER_URL,
				).toString()
			: null;

	console.log(`[DEBUG] Opengraph image for ${id}`, { imgUrl, concert });

	return new ImageResponse(
		<div
			style={{
				position: "relative",
				height: "100%",
				width: "100%",
				backgroundColor: "#95C11F",
				fontFamily: "Switzer",
				display: "flex",
			}}
		>
			{imgUrl && (
				<img
					src={imgUrl}
					alt={(concert?.image as Media)?.alt || ""}
					style={{ objectFit: "cover", height: "100%", width: "100%" }}
				/>
			)}
			<div
				style={{
					position: "absolute",
					inset: 0,
					color: "#fff",
					display: "flex",
					flexDirection: "column",
					justifyContent: "center",
					alignItems: "center",
					textAlign: "center",
					padding: 40,
				}}
			>
				<h1 style={{ fontSize: 64, fontWeight: 700, lineHeight: 1.2 }}>
					{concert?.title ?? "Hard Chor"}
				</h1>
				<p style={{ fontSize: 32, fontWeight: 400, lineHeight: 1.2 }}>
					{concert?.formattedDateString ?? ""}
				</p>
			</div>
		</div>,
		{
			...size,
			fonts: [
				{ name: "Switzer", data: SwitzerRegular, style: "normal", weight: 400 },
			],
		},
	);
}
