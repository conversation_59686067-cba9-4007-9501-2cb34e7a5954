import "@/css/index.css";

import type { Metadata, Viewport } from "next";
import localFont from "next/font/local";
import { ReactTempus } from "tempus/react";
import { ViewTransitions } from "next-view-transitions";

import { RealViewport } from "@/components/layout/real-viewport";
import { GSAP } from "@/components/gsap/gsap";

export const metadata: Metadata = {
	formatDetection: {
		telephone: false,
	},
	metadataBase: new URL(process.env.NEXT_PUBLIC_SERVER_URL || ""),
	authors: [{ name: "<PERSON>", url: "https://bnm.st" }],
};

export const viewport: Viewport = {
	themeColor: "#fff",
	colorScheme: "normal",
};

const sans = localFont({
	src: [
		{
			path: "../../../public/fonts/switzer/Switzer-Variable.woff2",
			style: "normal",
		},
	],
	display: "swap",
	variable: "--font-sans",
	preload: true,
});

export default function Layout({
	children,
	modal,
}: {
	children: React.ReactNode;
	modal: React.ReactNode;
}) {
	return (
		<ViewTransitions>
			<html
				lang="en"
				dir="ltr"
				className={`${sans.variable}`}
				suppressHydrationWarning
			>
				<body className="antialiased">
					<div id="meganav-root" />
					{modal}
					<RealViewport />
					{children}
					<GSAP />
					<ReactTempus patch />
				</body>
			</html>
		</ViewTransitions>
	);
}
