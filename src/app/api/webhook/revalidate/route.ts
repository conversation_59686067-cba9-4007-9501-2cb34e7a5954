import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
	try {
		// Get the webhook payload
		const body = await request.json();
		const { collection, operation, doc } = body;

		// Determine what needs to be revalidated based on the webhook data
		let revalidationPayload: any = {};

		switch (collection) {
			case "pages":
				// For pages, revalidate the specific page path
				if (doc?.slug) {
					const path = doc.slug === "home" ? "/" : `/${doc.slug}`;
					revalidationPayload = { paths: [path] };
				} else {
					revalidationPayload = { collection: "pages", action: operation };
				}
				break;

			case "concerts":
			case "composers":
			case "critics":
			case "programs":
				// For these collections, revalidate archive pages
				revalidationPayload = { collection, action: operation };
				break;

			case "header":
				// For header changes, revalidate all pages
				revalidationPayload = {};
				break;

			default:
				// For unknown collections, revalidate all pages
				revalidationPayload = {};
		}

		// Call the revalidation API
		const revalidateUrl = new URL("/api/revalidate", request.url);
		const revalidateResponse = await fetch(revalidateUrl.toString(), {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
				"Authorization": `Bearer ${process.env.REVALIDATE_SECRET}`,
			},
			body: JSON.stringify(revalidationPayload),
		});

		if (!revalidateResponse.ok) {
			throw new Error(`Revalidation failed: ${revalidateResponse.statusText}`);
		}

		const result = await revalidateResponse.json();

		return NextResponse.json({
			success: true,
			message: "Webhook processed and revalidation triggered",
			collection,
			operation,
			revalidationResult: result,
		});

	} catch (error) {
		console.error("Webhook revalidation error:", error);
		return NextResponse.json(
			{ 
				error: "Failed to process webhook", 
				details: error instanceof Error ? error.message : "Unknown error" 
			},
			{ status: 500 }
		);
	}
}
