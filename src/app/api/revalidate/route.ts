import { revalidatePath, revalidateTag } from "next/cache";
import { NextRequest, NextResponse } from "next/server";
import configPromise from "@payload-config";
import { getPayload } from "payload";

export async function POST(request: NextRequest) {
	try {
		// Verify the request is authorized (you might want to add authentication here)
		const authHeader = request.headers.get("authorization");
		if (!authHeader || authHeader !== `Bearer ${process.env.REVALIDATE_SECRET}`) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		const body = await request.json();
		const { paths, collection, action } = body;

		const payload = await getPayload({ config: configPromise });

		if (paths && Array.isArray(paths)) {
			// Revalidate specific paths
			for (const path of paths) {
				revalidatePath(path);
				console.log(`Revalidated path: ${path}`);
			}
			revalidateTag("pages-sitemap");
		} else if (collection && action) {
			// Trigger collection-specific revalidation
			switch (collection) {
				case "pages":
					// Revalidate all pages
					const pages = await payload.find({
						collection: "pages",
						limit: 0,
						select: { slug: true },
					});

					for (const page of pages.docs) {
						const path = page.slug === "home" ? "/" : `/${page.slug}`;
						revalidatePath(path);
						console.log(`Revalidated page: ${path}`);
					}
					break;

				case "concerts":
				case "composers":
				case "critics":
				case "programs":
					// Find pages that use archive blocks for this collection
					const archivePages = await payload.find({
						collection: "pages",
						limit: 0,
						where: {
							"layout.blockType": {
								equals: `${collection.slice(0, -1)}-archive`, // Remove 's' and add '-archive'
							},
						},
						select: { slug: true },
					});

					for (const page of archivePages.docs) {
						const path = page.slug === "home" ? "/" : `/${page.slug}`;
						revalidatePath(path);
						console.log(`Revalidated archive page: ${path}`);
					}
					break;

				default:
					// Revalidate all pages as fallback
					const allPages = await payload.find({
						collection: "pages",
						limit: 0,
						select: { slug: true },
					});

					for (const page of allPages.docs) {
						const path = page.slug === "home" ? "/" : `/${page.slug}`;
						revalidatePath(path);
						console.log(`Revalidated page: ${path}`);
					}
			}

			revalidateTag("pages-sitemap");
		} else {
			// Revalidate everything
			const allPages = await payload.find({
				collection: "pages",
				limit: 0,
				select: { slug: true },
			});

			for (const page of allPages.docs) {
				const path = page.slug === "home" ? "/" : `/${page.slug}`;
				revalidatePath(path);
				console.log(`Revalidated page: ${path}`);
			}

			revalidateTag("pages-sitemap");
		}

		return NextResponse.json({ 
			success: true, 
			message: "Revalidation completed",
			timestamp: new Date().toISOString()
		});

	} catch (error) {
		console.error("Revalidation error:", error);
		return NextResponse.json(
			{ error: "Failed to revalidate", details: error instanceof Error ? error.message : "Unknown error" },
			{ status: 500 }
		);
	}
}
