"use server";

import { revalidatePath, revalidateTag } from "next/cache";
import configPromise from "@payload-config";
import { getPayload } from "payload";

export async function revalidatePathAction(path: string) {
	try {
		revalidatePath(path);
		revalidateTag("pages-sitemap");
		console.log(`Successfully revalidated path: ${path}`);
	} catch (error) {
		console.error(`Failed to revalidate path ${path}:`, error);
	}
}

export async function revalidateMultiplePathsAction(paths: string[]) {
	try {
		for (const path of paths) {
			revalidatePath(path);
		}
		revalidateTag("pages-sitemap");
		console.log(`Successfully revalidated ${paths.length} paths`);
	} catch (error) {
		console.error("Failed to revalidate paths:", error);
	}
}

/**
 * Trigger revalidation for all pages by calling Payload operations with enableRevalidation context
 */
export async function triggerRevalidationAction() {
	try {
		const payload = await getPayload({ config: configPromise });

		// Find a page to trigger revalidation hooks
		const pages = await payload.find({
			collection: "pages",
			limit: 1,
			context: {
				enableRevalidation: true,
			},
		});

		if (pages.docs.length > 0) {
			// Update the page to trigger the revalidation hook
			await payload.update({
				collection: "pages",
				id: pages.docs[0].id,
				data: {
					// Just update the updatedAt timestamp
					updatedAt: new Date().toISOString(),
				},
				context: {
					enableRevalidation: true,
				},
			});
		}

		console.log("Successfully triggered revalidation");
	} catch (error) {
		console.error("Failed to trigger revalidation:", error);
	}
}
