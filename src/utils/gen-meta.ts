import type { Metadata } from "next";

import type { Page } from "../payload-types";

import { mergeOpenGraph } from "./merge-og";
import { projectInfo } from "./get-project-info";

export const generateMeta = async (args: { doc: Page }): Promise<Metadata> => {
	const { doc } = args || {};

	const ogImage =
		typeof doc?.meta?.image === "object" &&
		doc.meta.image !== null &&
		"url" in doc.meta.image &&
		`${process.env.NEXT_PUBLIC_SERVER_URL}${doc.meta.image.url}`;

	const title = doc?.meta?.title
		? `${doc?.meta?.title} | Hard Chor`
		: "Hard Chor";

	return {
		description: doc?.meta?.description,
		openGraph: mergeOpenGraph({
			description: doc?.meta?.description || "",
			images: ogImage
				? [
						{
							url: ogImage,
						},
					]
				: undefined,
			title,
			url: Array.isArray(doc?.slug) ? doc?.slug.join("/") : "/",
		}),
		title,
	};
};
